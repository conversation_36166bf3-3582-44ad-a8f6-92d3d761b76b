PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - audio_session (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - Flutter (1.0.0)
  - flutter_pcm_sound (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_tts (0.0.1):
    - Flutter
  - flutter_unity_widget (4.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - onnxruntime (0.0.1):
    - Flutter
    - onnxruntime-objc (= 1.15.1)
  - onnxruntime-c (1.15.1)
  - onnxruntime-objc (1.15.1):
    - onnxruntime-objc/Core (= 1.15.1)
  - onnxruntime-objc/Core (1.15.1):
    - onnxruntime-c (= 1.15.1)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - record_ios (1.0.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - speech_to_text (0.0.1):
    - Flutter
    - Try
  - Try (2.1.1)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - Flutter (from `Flutter`)
  - flutter_pcm_sound (from `.symlinks/plugins/flutter_pcm_sound/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - flutter_unity_widget (from `.symlinks/plugins/flutter_unity_widget/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - onnxruntime (from `.symlinks/plugins/onnxruntime/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - record_ios (from `.symlinks/plugins/record_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - onnxruntime-c
    - onnxruntime-objc
    - PromisesObjC
    - Try

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  Flutter:
    :path: Flutter
  flutter_pcm_sound:
    :path: ".symlinks/plugins/flutter_pcm_sound/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  flutter_unity_widget:
    :path: ".symlinks/plugins/flutter_unity_widget/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  onnxruntime:
    :path: ".symlinks/plugins/onnxruntime/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  record_ios:
    :path: ".symlinks/plugins/record_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  audio_waveforms: cd736909ebc6b6a164eb74701d8c705ce3241e1c
  audioplayers_darwin: 4027b33a8f471d996c13f71cb77f0b1583b5d923
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_pcm_sound: de0572ca4f99091cc2abfcc31601b8a4ddd33c0e
  flutter_secure_storage: d33dac7ae2ea08509be337e775f6b59f1ff45f12
  flutter_tts: 0f492aab6accf87059b72354fcb4ba934304771d
  flutter_unity_widget: e509f3109933aa0c63b1bc149ca3d5b90fb68543
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  onnxruntime: e9346181d75b8dea8733bdae512a22c298962e00
  onnxruntime-c: ebdcfd8650bcbd10121c125262f99dea681b92a3
  onnxruntime-objc: ae7acec7a3d03eaf072d340afed7a35635c1c2a6
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  record_ios: 1bbc430ab8406174d70332f23e06a3dc751239b4
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  speech_to_text: b43a7d99aef037bd758ed8e45d79bbac035d2dfe
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: 53a6aebc29ccee84c41f92f409fc20cd4ca011f1

COCOAPODS: 1.16.2
