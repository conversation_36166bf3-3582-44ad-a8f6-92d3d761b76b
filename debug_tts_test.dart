import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:ellahai/services/storage/storage_service.dart';

void main() {
  group('TTS Auto-play Settings Test', () {
    test('Check auto-play voice setting', () async {
      // Initialize Hive and storage service
      await Hive.initFlutter();
      await StorageService.init();
      
      // Check current auto-play setting
      final isAutoPlayEnabled = StorageService.isAutoPlayVoiceEnabled();
      print('🔊 Auto-play voice enabled: $isAutoPlayEnabled');
      
      // Check voice enabled setting
      final isVoiceEnabled = StorageService.isVoiceEnabled();
      print('🔊 Voice enabled: $isVoiceEnabled');
      
      // Set auto-play to true for testing
      await StorageService.setAutoPlayVoice(true);
      await StorageService.setVoiceEnabled(true);
      
      // Verify settings
      final newAutoPlaySetting = StorageService.isAutoPlayVoiceEnabled();
      final newVoiceSetting = StorageService.isVoiceEnabled();
      
      print('🔊 After setting - Auto-play: $newAutoPlaySetting, Voice: $newVoiceSetting');
      
      expect(newAutoPlaySetting, true);
      expect(newVoiceSetting, true);
    });
  });
}
