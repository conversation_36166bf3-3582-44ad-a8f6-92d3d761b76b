import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/voice/voice_to_voice_service.dart';

/// State class for voice-to-voice functionality
class VoiceToVoiceState {
  final bool isInitialized;
  final bool isListening;
  final bool isBuffering;
  final String? currentTranscription;
  final String? currentResponse;
  final Map<String, dynamic>? currentEmotion;
  final String? error;

  const VoiceToVoiceState({
    this.isInitialized = false,
    this.isListening = false,
    this.isBuffering = false,
    this.currentTranscription,
    this.currentResponse,
    this.currentEmotion,
    this.error,
  });

  VoiceToVoiceState copyWith({
    bool? isInitialized,
    bool? isListening,
    bool? isBuffering,
    String? currentTranscription,
    String? currentResponse,
    Map<String, dynamic>? currentEmotion,
    String? error,
  }) {
    return VoiceToVoiceState(
      isInitialized: isInitialized ?? this.isInitialized,
      isListening: isListening ?? this.isListening,
      isBuffering: isBuffering ?? this.isBuffering,
      currentTranscription: currentTranscription ?? this.currentTranscription,
      currentResponse: currentResponse ?? this.currentResponse,
      currentEmotion: currentEmotion ?? this.currentEmotion,
      error: error ?? this.error,
    );
  }
}

/// Notifier for voice-to-voice state management
class VoiceToVoiceNotifier extends StateNotifier<VoiceToVoiceState> {
  final VoiceToVoiceService _service;

  VoiceToVoiceNotifier(this._service) : super(const VoiceToVoiceState()) {
    _setupListeners();
  }

  /// Set up listeners for service streams
  void _setupListeners() {
    // Listen to transcription stream
    _service.transcriptionStream.listen((transcription) {
      state = state.copyWith(currentTranscription: transcription);
    });

    // Listen to response stream
    _service.responseStream.listen((response) {
      state = state.copyWith(currentResponse: response);
    });

    // Listen to emotion stream
    _service.emotionStream.listen((emotion) {
      state = state.copyWith(currentEmotion: emotion);
    });

    // Listen to error stream
    _service.errorStream.listen((error) {
      state = state.copyWith(error: error);
    });

    // Listen to buffering stream
    _service.bufferingStream.listen((isBuffering) {
      state = state.copyWith(isBuffering: isBuffering);
    });
  }

  /// Initialize the voice-to-voice service
  Future<bool> initialize({String? conversationId}) async {
    try {
      final success = await _service.initialize(conversationId: conversationId);
      state = state.copyWith(
        isInitialized: success,
        error: success ? null : 'Failed to initialize voice service',
      );
      return success;
    } catch (e) {
      state = state.copyWith(
        isInitialized: false,
        error: 'Initialization error: $e',
      );
      return false;
    }
  }

  /// Start listening for voice input
  Future<bool> startListening({String? conversationId}) async {
    if (!state.isInitialized) {
      state = state.copyWith(error: 'Service not initialized');
      return false;
    }

    try {
      // Immediately update state to show listening
      state = state.copyWith(isListening: true, error: null);
      
      final success = await _service.startListening(conversationId: conversationId);
      
      if (!success) {
        // Revert state if failed
        state = state.copyWith(
          isListening: false,
          error: 'Failed to start listening',
        );
      }
      
      return success;
    } catch (e) {
      state = state.copyWith(
        isListening: false,
        error: 'Failed to start listening: $e',
      );
      return false;
    }
  }

  /// Stop listening for voice input
  Future<void> stopListening() async {
    try {
      // Immediately update state to show not listening
      state = state.copyWith(isListening: false);
      
      await _service.stopListening();
    } catch (e) {
      state = state.copyWith(error: 'Failed to stop listening: $e');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear current transcription
  void clearTranscription() {
    state = state.copyWith(currentTranscription: null);
  }

  /// Clear current response
  void clearResponse() {
    state = state.copyWith(currentResponse: null);
  }

  /// Set buffering state
  void setBuffering(bool isBuffering) {
    state = state.copyWith(isBuffering: isBuffering);
  }
}

/// Provider for voice-to-voice state
final voiceToVoiceProvider = StateNotifierProvider<VoiceToVoiceNotifier, VoiceToVoiceState>((ref) {
  final service = ref.watch(voiceToVoiceServiceProvider);
  return VoiceToVoiceNotifier(service);
});

/// Convenience providers for specific state properties
final isVoiceListeningProvider = Provider<bool>((ref) {
  return ref.watch(voiceToVoiceProvider).isListening;
});

final voiceTranscriptionProvider = Provider<String?>((ref) {
  return ref.watch(voiceToVoiceProvider).currentTranscription;
});

final voiceResponseProvider = Provider<String?>((ref) {
  return ref.watch(voiceToVoiceProvider).currentResponse;
});

final voiceEmotionProvider = Provider<Map<String, dynamic>?>((ref) {
  return ref.watch(voiceToVoiceProvider).currentEmotion;
});

final voiceErrorProvider = Provider<String?>((ref) {
  return ref.watch(voiceToVoiceProvider).error;
});

final isVoiceBufferingProvider = Provider<bool>((ref) {
  return ref.watch(voiceToVoiceProvider).isBuffering;
});
