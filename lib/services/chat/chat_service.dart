import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../models/chat/message_model.dart';
import '../auth/auth_service.dart';
import '../audio/audio_playback_service.dart';
import '../storage/storage_service.dart';
import 'websocket_service.dart';

class ConversationModel {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime updatedAt;
  final MessageModel? lastMessage;
  final int unreadCount;

  ConversationModel({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessage,
    this.unreadCount = 0,
  });

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'],
      title: json['title'] ?? 'Conversation',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      lastMessage: json['last_message'] != null 
          ? MessageModel.fromJson(json['last_message'])
          : null,
      unreadCount: json['unread_count'] ?? 0,
    );
  }
}

class ChatService {
  final AuthService _authService;
  final WebSocketService _webSocketService;
  final AudioPlaybackService _audioPlaybackService;
  late final Dio _dio;

  // Stream controllers for real-time updates
  final _messagesController = StreamController<List<MessageModel>>.broadcast();
  final _conversationsController = StreamController<List<ConversationModel>>.broadcast();
  final _typingController = StreamController<bool>.broadcast();
  final _transcriptionController = StreamController<String>.broadcast();
  final _emotionController = StreamController<Map<String, dynamic>>.broadcast();
  final _ttsAudioController = StreamController<Map<String, dynamic>>.broadcast();
  final _currentLLMResponseController = StreamController<String>.broadcast();

  // Current state
  String? _currentConversationId;
  final Map<String, List<MessageModel>> _conversationMessages = {};
  final List<ConversationModel> _conversations = [];

  // Track current TTS request to avoid multiple startTTSPlayback calls
  String? _currentTTSRequestId;

  // Track current streaming LLM response
  String _currentStreamingResponse = '';
  String? _currentStreamingChunkId;
  Timer? _streamingDisplayTimer;

  ChatService(this._authService, this._webSocketService, this._audioPlaybackService) {
    _initializeDio();
    _setupWebSocketListeners();
  }

  // Getters
  Stream<List<MessageModel>> get messagesStream => _messagesController.stream;
  Stream<List<ConversationModel>> get conversationsStream => _conversationsController.stream;
  Stream<bool> get typingStream => _typingController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Map<String, dynamic>> get emotionStream => _emotionController.stream;
  Stream<Map<String, dynamic>> get ttsAudioStream => _ttsAudioController.stream;
  Stream<String> get currentLLMResponseStream => _currentLLMResponseController.stream;
  
  String? get currentConversationId => _currentConversationId;
  List<MessageModel> get currentMessages => 
      _currentConversationId != null 
          ? _conversationMessages[_currentConversationId!] ?? []
          : [];

  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add auth interceptor
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await _authService.getAccessToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
    ));
  }

  void _setupWebSocketListeners() {
    _webSocketService.messageStream.listen((wsMessage) {
      _handleWebSocketMessage(wsMessage);
    });
  }

  void _handleWebSocketMessage(WebSocketMessage wsMessage) {
    switch (wsMessage.type) {
      case 'llm_response_chunk':
        _handleLLMResponseChunk(wsMessage.data);
        break;
      case 'transcription_partial':
        _handleTranscriptionPartial(wsMessage.data);
        break;
      case 'emotion_detected':
        _handleEmotionDetected(wsMessage.data);
        break;
      case 'audio_chunk':
        _handleTTSAudioChunk(wsMessage.data);
        break;
      case 'ai_typing':
        _handleAITyping(wsMessage.data);
        break;
      case 'message_received':
        _handleMessageReceived(wsMessage.data);
        break;
      case 'conversation_created':
        _handleConversationCreated(wsMessage.data);
        break;
    }
  }

  void _handleLLMResponseChunk(Map<String, dynamic> data) {
    final content = data['content'] ?? '';
    final chunkId = data['chunk_id'];
    final isFinal = data['is_final'] ?? false;
    final conversationId = data['conversation_id'] ?? _currentConversationId;
    final isCached = data['is_cached'] ?? false;

    // Handle streaming response for real-time display
    if (chunkId != _currentStreamingChunkId) {
      // New streaming response started - reset the accumulated response
      _currentStreamingResponse = '';
      _currentStreamingChunkId = chunkId;

      // Cancel any existing timer
      _streamingDisplayTimer?.cancel();
    }

    // Always append the new content to build the complete response
    _currentStreamingResponse += content;

    // For cached responses, show the complete response immediately when final
    // For real streaming responses, show each chunk as it arrives
    if (isCached && isFinal) {
      // Show complete cached response immediately
      _currentLLMResponseController.add(_currentStreamingResponse);
    } else if (!isCached) {
      // Show streaming response in real-time
      _currentLLMResponseController.add(_currentStreamingResponse);
    }
    // For cached responses that aren't final yet, don't show anything to avoid flashing

    if (conversationId != null) {
      // Find or create the AI message being streamed
      final messages = _conversationMessages[conversationId] ?? [];
      final existingIndex = messages.indexWhere(
        (m) => m.metadata?['chunk_id'] == chunkId && !m.isFromUser,
      );

      if (existingIndex != -1) {
        // Update existing message
        final existingMessage = messages[existingIndex];
        final updatedMessage = existingMessage.copyWith(
          content: existingMessage.content + content,
          metadata: {
            ...existingMessage.metadata ?? {},
            'is_final': isFinal,
          },
        );
        messages[existingIndex] = updatedMessage;
      } else {
        // Create new message
        final newMessage = MessageModel.text(
          id: chunkId,
          content: content,
          isFromUser: false,
          conversationId: conversationId,
          metadata: {
            'chunk_id': chunkId,
            'is_final': isFinal,
            'streaming': true,
          },
        );
        messages.add(newMessage);
      }

      _conversationMessages[conversationId] = messages;
      if (conversationId == _currentConversationId) {
        _messagesController.add(messages);
      }
    }
  }

  void _handleTranscriptionPartial(Map<String, dynamic> data) {
    final text = data['text'] ?? '';
    final isPartial = data['is_partial'] ?? true;
    
    if (!isPartial) {
      // Final transcription - this will be used for the actual message
      _transcriptionController.add(text);
    }
  }

  void _handleEmotionDetected(Map<String, dynamic> data) {
    _emotionController.add(data);
  }

  void _handleTTSAudioChunk(Map<String, dynamic> data) {
    // Emit to stream for other listeners
    _ttsAudioController.add(data);

    // Check if auto-play is enabled
    final isAutoPlayEnabled = StorageService.isAutoPlayVoiceEnabled();
    debugPrint('ChatService: 🔊 TTS Audio Chunk Received! Auto-play voice enabled: $isAutoPlayEnabled');
    debugPrint('ChatService: 🔊 Raw TTS data keys: ${data.keys.toList()}');

    if (!isAutoPlayEnabled) {
      debugPrint('ChatService: ❌ Auto-play disabled, skipping TTS audio playback');
      return;
    }

    // Handle audio playback
    try {
      final audioDataB64 = data['data'] as String?;
      final chunkId = data['chunk_id'] as String?;
      final isFinal = data['is_final'] as bool? ?? false;
      final requestId = data['request_id'] as String?;

      // Extract audio format metadata for debugging
      final sampleRate = data['sample_rate'] as int?;
      final channels = data['channels'] as int?;
      final bitDepth = data['bit_depth'] as int?;
      final format = data['format'] as String?;

      debugPrint('ChatService: TTS chunk data - audioData: ${audioDataB64?.length ?? 0} chars, chunkId: $chunkId, isFinal: $isFinal, requestId: $requestId');
      debugPrint('ChatService: Audio format - sampleRate: $sampleRate, channels: $channels, bitDepth: $bitDepth, format: $format');

      if (audioDataB64 != null && chunkId != null) {
        // Decode base64 audio data
        final audioData = base64Decode(audioDataB64);

        debugPrint('ChatService: Received TTS audio chunk $chunkId (${audioData.length} bytes, final: $isFinal)');

        // Log audio format analysis
        if (audioData.length >= 4) {
          // Analyze first few samples to detect potential format issues
          final firstSample = (audioData[1] << 8) | audioData[0]; // Little-endian 16-bit
          final secondSample = (audioData[3] << 8) | audioData[2];
          debugPrint('ChatService: First samples: $firstSample, $secondSample (raw bytes: ${audioData.take(8).toList()})');
        }

        // Start TTS playback session only if this is a new request
        if (requestId != null && requestId != _currentTTSRequestId) {
          debugPrint('ChatService: Starting TTS playback session for new request: $requestId');
          _currentTTSRequestId = requestId;
          _audioPlaybackService.startTTSPlayback(requestId);
        }

        // Add to audio playback service
        // Backend is hardcoded to 16kHz, 1 channel, 16-bit PCM (see backend_consumer.txt line 375)
        _audioPlaybackService.addAudioChunk(
          audioData: audioData,
          chunkId: chunkId,
          isFinal: isFinal,
          requestId: requestId,
          sampleRate: sampleRate ?? 16000, // Backend default
          channels: channels ?? 1, // Backend default
          bitDepth: bitDepth ?? 16, // Backend default
          format: format ?? 'PCM', // Backend default
        );

        // Reset current TTS request ID when final chunk is received
        if (isFinal) {
          _currentTTSRequestId = null;
        }
      } else {
        debugPrint('ChatService: Invalid TTS audio chunk data - audioDataB64: ${audioDataB64 != null}, chunkId: ${chunkId != null}');
      }
    } catch (e) {
      debugPrint('ChatService: Error handling TTS audio chunk: $e');
    }
  }

  void _handleAITyping(Map<String, dynamic> data) {
    final status = data['status'];
    _typingController.add(status == 'started');
  }

  void _handleMessageReceived(Map<String, dynamic> data) {
    // Message was successfully received by the server
    final messageId = data['message_id'];
    final conversationId = data['conversation_id'] ?? _currentConversationId;
    
    if (conversationId != null) {
      final messages = _conversationMessages[conversationId] ?? [];
      final messageIndex = messages.indexWhere((m) => m.id == messageId);
      if (messageIndex != -1) {
        final updatedMessage = messages[messageIndex].markAsDelivered();
        messages[messageIndex] = updatedMessage;
        _conversationMessages[conversationId] = messages;
        
        if (conversationId == _currentConversationId) {
          _messagesController.add(messages);
        }
      }
    }
  }

  void _handleConversationCreated(Map<String, dynamic> data) {
    final conversation = ConversationModel.fromJson(data);
    _conversations.add(conversation);
    _conversationsController.add(_conversations);
  }

  // API Methods
  Future<List<ConversationModel>> loadConversations() async {
    Response? response;
    try {
      response = await _dio.get('/api/conversations/');

      // Handle both List and Map response formats
      List<dynamic> conversationData;
      if (response.data is List) {
        conversationData = response.data as List;
      } else if (response.data is Map && response.data['conversations'] != null) {
        conversationData = response.data['conversations'] as List;
      } else if (response.data is Map && response.data['results'] != null) {
        conversationData = response.data['results'] as List;
      } else {
        // If response is a single conversation object, wrap it in a list
        conversationData = [response.data];
      }

      final conversations = conversationData
          .map((json) => ConversationModel.fromJson(json))
          .toList();

      _conversations.clear();
      _conversations.addAll(conversations);
      _conversationsController.add(_conversations);

      return conversations;
    } catch (e) {
      debugPrint('ChatService: Error loading conversations: $e');
      if (response != null) {
        debugPrint('ChatService: Response data type: ${response.data.runtimeType}');
        debugPrint('ChatService: Response data: ${response.data}');
      }

      // If backend is not available, create a default conversation for testing
      if (e.toString().contains('Connection') || e.toString().contains('timeout')) {
        debugPrint('ChatService: Backend not available, creating default conversation for testing');
        final defaultConversation = ConversationModel(
          id: 'default-conversation-${DateTime.now().millisecondsSinceEpoch}',
          title: 'Default Chat (Offline)',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        _conversations.clear();
        _conversations.add(defaultConversation);
        _conversationsController.add(_conversations);

        return [defaultConversation];
      }

      throw ChatException('Failed to load conversations: $e');
    }
  }

  Future<ConversationModel> createConversation({String? title}) async {
    try {
      final response = await _dio.post('/api/conversations/', data: {
        'title': title ?? 'New Conversation',
      });
      
      final conversation = ConversationModel.fromJson(response.data);
      _conversations.add(conversation);
      _conversationsController.add(_conversations);
      
      return conversation;
    } catch (e) {
      throw ChatException('Failed to create conversation: $e');
    }
  }

  Future<List<MessageModel>> loadMessages(String conversationId) async {
    try {
      final response = await _dio.get('/api/conversations/$conversationId/messages/');
      final messages = (response.data as List)
          .map((json) => MessageModel.fromJson(json))
          .toList();
      
      _conversationMessages[conversationId] = messages;
      if (conversationId == _currentConversationId) {
        _messagesController.add(messages);
      }
      
      return messages;
    } catch (e) {
      throw ChatException('Failed to load messages: $e');
    }
  }

  Future<void> switchConversation(String conversationId) async {
    _currentConversationId = conversationId;
    
    // Load messages if not already loaded
    if (!_conversationMessages.containsKey(conversationId)) {
      await loadMessages(conversationId);
    } else {
      _messagesController.add(_conversationMessages[conversationId]!);
    }
    
    // Switch WebSocket conversation
    _webSocketService.switchConversation(conversationId);
  }

  Future<void> sendTextMessage(String content, {String? conversationId}) async {
    // Clear previous AI response when user starts new interaction
    _streamingDisplayTimer?.cancel();
    _currentStreamingResponse = '';
    _currentStreamingChunkId = null;
    _currentLLMResponseController.add('');

    // Ensure WebSocket is connected
    if (!_webSocketService.isConnected) {
      debugPrint('ChatService: WebSocket not connected, attempting to connect...');
      try {
        await _webSocketService.connect();
      } catch (e) {
        throw ChatException('Failed to connect to WebSocket: $e');
      }
    }

    conversationId ??= _currentConversationId;

    // If no conversation exists, let the backend handle conversation creation via WebSocket
    if (conversationId == null) {
      // Generate a temporary conversation ID that the backend will use or replace
      conversationId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
      _currentConversationId = conversationId;
      debugPrint('ChatService: Using temporary conversation ID: $conversationId');
    }

    // Create local message immediately
    final message = MessageModel.text(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isFromUser: true,
      conversationId: conversationId,
    );

    // Add to local state
    final messages = _conversationMessages[conversationId] ?? [];
    messages.add(message);
    _conversationMessages[conversationId] = messages;

    if (conversationId == _currentConversationId) {
      _messagesController.add(messages);
    }

    // Send via WebSocket - let backend handle conversation creation if needed
    debugPrint('ChatService: Sending text message via WebSocket to conversation: $conversationId');
    _webSocketService.sendTextMessage(content, conversationId: conversationId);
  }

  Future<void> deleteConversation(String conversationId) async {
    try {
      await _dio.delete('/api/conversations/$conversationId/');
      
      _conversations.removeWhere((c) => c.id == conversationId);
      _conversationMessages.remove(conversationId);
      _conversationsController.add(_conversations);
      
      if (_currentConversationId == conversationId) {
        _currentConversationId = null;
        _messagesController.add([]);
      }
    } catch (e) {
      throw ChatException('Failed to delete conversation: $e');
    }
  }

  void dispose() {
    _streamingDisplayTimer?.cancel();
    _messagesController.close();
    _conversationsController.close();
    _typingController.close();
    _transcriptionController.close();
    _emotionController.close();
    _ttsAudioController.close();
    _currentLLMResponseController.close();
  }
}

class ChatException implements Exception {
  final String message;
  ChatException(this.message);
  
  @override
  String toString() => 'ChatException: $message';
}
