import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../../providers/voice_to_voice_provider.dart';
import '../../providers/chat_provider.dart';

class RealtimeVoiceButton extends ConsumerStatefulWidget {
  final Function(String transcription) onTranscription;
  final double size;
  final bool showVadEvents;

  const RealtimeVoiceButton({
    super.key,
    required this.onTranscription,
    this.size = 56,
    this.showVadEvents = false,
  });

  @override
  ConsumerState<RealtimeVoiceButton> createState() => _RealtimeVoiceButtonState();
}

class _RealtimeVoiceButtonState extends ConsumerState<RealtimeVoiceButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    // Initialize the voice-to-voice service
    _initializeVoiceToVoiceService();
  }

  Future<void> _initializeVoiceToVoiceService() async {
    try {
      // Get current conversation ID from chat provider
      final currentConversationId = ref.read(currentConversationIdProvider);

      // Ensure WebSocket is connected first
      final chatNotifier = ref.read(chatProvider.notifier);
      final isConnected = ref.read(isChatConnectedProvider);

      if (!isConnected) {
        debugPrint('RealtimeVoiceButton: WebSocket not connected, attempting to connect...');
        await chatNotifier.reconnect();

        // Wait a bit for connection to establish
        await Future.delayed(const Duration(milliseconds: 500));

        final stillNotConnected = !ref.read(isChatConnectedProvider);
        if (stillNotConnected) {
          debugPrint('RealtimeVoiceButton: WebSocket connection failed. Voice features may not work properly.');
          // Continue with initialization anyway for offline testing
        }
      }

      // Initialize voice service with conversation context
      final success = await ref.read(voiceToVoiceProvider.notifier).initialize(
        conversationId: currentConversationId,
      );

      setState(() {
        _isInitialized = success;
      });

      if (!success) {
        _showError('Failed to initialize voice service');
      } else {
        debugPrint('RealtimeVoiceButton: Voice service initialized successfully with conversation: $currentConversationId');
      }
    } catch (e) {
      debugPrint('RealtimeVoiceButton: Initialization error: $e');
      _showError('Failed to initialize voice service: $e');
      setState(() {
        _isInitialized = false;
      });
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _handleVoiceButtonPressed() async {
    if (!_isInitialized) {
      _showError('Voice service not initialized');
      return;
    }

    final voiceNotifier = ref.read(voiceToVoiceProvider.notifier);
    final isListening = ref.read(isVoiceListeningProvider);

    HapticFeedback.mediumImpact();

    if (isListening) {
      await voiceNotifier.stopListening();
      _pulseController.stop();
    } else {
      // Get current conversation ID for context
      final currentConversationId = ref.read(currentConversationIdProvider);

      // Ensure WebSocket is still connected before starting
      final isConnected = ref.read(isChatConnectedProvider);
      if (!isConnected) {
        debugPrint('RealtimeVoiceButton: WebSocket disconnected, attempting to reconnect...');
        await ref.read(chatProvider.notifier).reconnect();
        await Future.delayed(const Duration(milliseconds: 300));
      }

      final success = await voiceNotifier.startListening(conversationId: currentConversationId);
      if (success) {
        _pulseController.repeat(reverse: true);
        debugPrint('RealtimeVoiceButton: Started listening with conversation: $currentConversationId');
      } else {
        _showError('Failed to start voice listening');
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildVoiceButton() {
    return Consumer(
      builder: (context, ref, child) {
        final isListening = ref.watch(isVoiceListeningProvider);
        final isBuffering = ref.watch(isVoiceBufferingProvider);
        final error = ref.watch(voiceErrorProvider);

        // Listen for transcriptions and pass to callback
        ref.listen<String?>(voiceTranscriptionProvider, (previous, next) {
          if (next != null && next.isNotEmpty && next != previous) {
            widget.onTranscription(next);
          }
        });

        // Show error if any
        if (error != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showError(error);
            ref.read(voiceToVoiceProvider.notifier).clearError();
          });
        }

        Color buttonColor;
        IconData iconData;
        String tooltip;

        if (!_isInitialized) {
          buttonColor = Colors.grey;
          iconData = Icons.mic_off;
          tooltip = 'Initializing voice service...';
        } else if (isListening) {
          if (isBuffering) {
            buttonColor = AppTheme.accentColor; // Different color for buffering
            iconData = Icons.pause;
            tooltip = 'Collecting speech... Tap to stop';
          } else {
            buttonColor = AppTheme.primaryColor;
            iconData = Icons.stop;
            tooltip = 'Tap to stop listening';
          }
        } else {
          buttonColor = AppTheme.primaryColor;
          iconData = Icons.mic;
          tooltip = 'Tap to start voice input';
        }

        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: 48, // Fixed smaller size
                height: 48,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: buttonColor,
                  boxShadow: [
                    BoxShadow(
                      color: buttonColor.withValues(alpha: 0.3),
                      blurRadius: 6, // Reduced blur for smaller button
                      spreadRadius: 1, // Reduced spread
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(24), // Adjusted for smaller size
                    onTap: _isInitialized ? _handleVoiceButtonPressed : null,
                    onTapDown: (_) => _scaleController.forward(),
                    onTapUp: (_) => _scaleController.reverse(),
                    onTapCancel: () => _scaleController.reverse(),
                    child: Tooltip(
                      message: tooltip,
                      child: Center(
                        child: Icon(
                          iconData,
                          color: Colors.white,
                          size: 20, // Fixed icon size for smaller button
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildPulseEffect() {
    return Consumer(
      builder: (context, ref, child) {
        final isListening = ref.watch(isVoiceListeningProvider);

        if (!isListening) return const SizedBox.shrink();

        return AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            // Create a subtle pulse ring that grows slightly beyond the button
            final pulseSize = 48 + (8 * (_pulseAnimation.value - 1.0)); // Grows 8px beyond button
            return Container(
              width: pulseSize,
              height: pulseSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.6 * (2.0 - _pulseAnimation.value)), // Fade as it grows
                  width: 1.0,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildVadEventsDisplay() {
    if (!widget.showVadEvents) return const SizedBox.shrink();

    return Consumer(
      builder: (context, ref, child) {
        final voiceState = ref.watch(voiceToVoiceProvider);

        // Show simple status for voice-to-voice service
        return Container(
          margin: const EdgeInsets.only(top: 8),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Voice-to-Voice Status',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Initialized: ${voiceState.isInitialized}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                'Listening: ${voiceState.isListening}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                'Buffering: ${voiceState.isBuffering}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: voiceState.isBuffering ? Colors.orange : Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProcessingInfo() {
    // Removed the processing info display to avoid UI clutter
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 56, // Slightly larger to accommodate pulse effect
          height: 56,
          child: Stack(
            alignment: Alignment.center,
            children: [
              _buildPulseEffect(),
              _buildVoiceButton(),
            ],
          ),
        ),
        _buildVadEventsDisplay(),
        _buildProcessingInfo(),
      ],
    );
  }
}

extension ListExtension<T> on List<T> {
  List<T> takeLast(int count) {
    if (count >= length) return this;
    return sublist(length - count);
  }
}
