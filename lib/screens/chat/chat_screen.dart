import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/chat_provider.dart';
import '../../providers/unity_provider.dart';
import '../../providers/chat_tools_provider.dart';
import '../../providers/task_provider.dart';

import '../../widgets/navigation/main_navigation.dart';
import '../../widgets/common/glassmorphic_container.dart';
import '../../widgets/chat/realtime_voice_button.dart';
import '../../widgets/task/task_overlay.dart';
import '../../models/chat_tool.dart';
import '../../models/task/task_model.dart';

class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({super.key});

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();
  
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  
  bool _showScrollToBottom = false;
  bool _showTaskOverlay = false;

  @override
  void initState() {
    super.initState();
    
    _fabAnimationController = AnimationController(
      duration: AppConstants.defaultAnimationDuration,
      vsync: this,
    );
    
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeInOut,
    ));

    _scrollController.addListener(_onScroll);
    _messageFocusNode.addListener(_onFocusChange);
    
    // Auto-scroll to bottom when new messages arrive
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom(animated: false);
      _addDemoTasks(); // Add demo tasks for testing
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final showButton = _scrollController.offset > 100;
    if (showButton != _showScrollToBottom) {
      setState(() => _showScrollToBottom = showButton);
      if (showButton) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }
  }

  void _onFocusChange() {
    // Focus change handling if needed in the future
  }

  void _scrollToBottom({bool animated = true}) {
    if (_scrollController.hasClients) {
      if (animated) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: AppConstants.defaultAnimationDuration,
          curve: Curves.easeOut,
        );
      } else {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    // Force ChatProvider initialization by watching it
    final chatState = ref.watch(chatProvider);
    print('ChatScreen: Chat state - isConnected: ${chatState.isConnected}, error: ${chatState.error}');

    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Stack(
            children: [
              // Unity Avatar Background - Full Height
              _buildUnityAvatar(),
              // App Bar at the top
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildAppBar(user?.name ?? 'User'),
              ),
              // Navigation and Input Area at the bottom
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: _buildInputArea(),
              ),
              // Scroll to bottom FAB
              if (_showScrollToBottom) _buildScrollToBottomFAB(),
              // Task overlay
              if (_showTaskOverlay) _buildTaskOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAIAgentStatusButton() {
    return Consumer(
      builder: (context, ref, child) {
        final activeTasks = ref.watch(activeTasksProvider);
        final hasActiveTasks = activeTasks.isNotEmpty;

        if (!hasActiveTasks) {
          return const SizedBox.shrink();
        }

        final overallProgress = activeTasks.isEmpty
            ? 0.0
            : activeTasks.map((task) => task.progress).reduce((a, b) => a + b) / activeTasks.length;

        return GestureDetector(
          onTap: () {
            setState(() {
              _showTaskOverlay = true;
            });
          },
          child: Container(
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.psychology_rounded,
                    color: AppTheme.primaryColor,
                    size: 10,
                  ),
                ).animate(onPlay: (controller) => controller.repeat())
                    .shimmer(duration: 2000.ms, color: Colors.white.withValues(alpha: 0.3)),
                const SizedBox(width: 6),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${activeTasks.length} task${activeTasks.length == 1 ? '' : 's'}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Container(
                      width: 40,
                      height: 2,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(1),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: overallProgress,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      ),
                    ).animate(onPlay: (controller) => controller.repeat())
                        .shimmer(duration: 1500.ms, color: Colors.white.withValues(alpha: 0.5)),
                  ],
                ),
                const SizedBox(width: 4),
                const Icon(
                  Icons.keyboard_arrow_up_rounded,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ).animate(onPlay: (controller) => controller.repeat())
              .shimmer(duration: 3000.ms, color: Colors.white.withValues(alpha: 0.2)),
        );
      },
    );
  }

  Widget _buildAppBar(String userName) {
    return Consumer(
      builder: (context, ref, child) {
        final isConnected = ref.watch(isChatConnectedProvider);
        final isTyping = ref.watch(isAITypingProvider);

        return GlassmorphicAppBar(
          titleWidget: Row(
            children: [
              Stack(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.psychology_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  // Connection status indicator
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: isConnected ? Colors.green : Colors.red,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'EllahAI',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      isTyping
                          ? 'Typing...'
                          : isConnected
                              ? 'Online'
                              : 'Connecting...',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isTyping
                            ? AppTheme.primaryColor
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // AI Agent Status Button (collapsible)
            _buildAIAgentStatusButton(),
            // Connection status button
            IconButton(
              onPressed: () {
                if (!isConnected) {
                  ref.read(chatProvider.notifier).reconnect();
                }
              },
              icon: Icon(
                isConnected ? Icons.wifi : Icons.wifi_off,
                color: isConnected ? Colors.green : Colors.red,
              ),
            ),
            IconButton(
              onPressed: () {
                // Open shop
                ref.read(navigationIndexProvider.notifier).state = 1;
              },
              icon: const Icon(Icons.store_rounded),
            ),
            IconButton(
              onPressed: () {
                // Open profile
                ref.read(navigationIndexProvider.notifier).state = 2;
              },
              icon: const Icon(Icons.person_rounded),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUnityAvatar() {
    return Consumer(
      builder: (context, ref, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child: UnityWidget(
                  onUnityCreated: (controller) {
                    ref.read(unityControllerProvider.notifier).setController(controller);
                  },
                  onUnityMessage: (message) {
                    ref.read(unityControllerProvider.notifier).handleMessage(message);
                  },
                  fullscreen: false,
                ),
        );
      },
    );
  }





  Widget _buildToolsSection() {
    return Container(
      height: 36,
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 0),
      child: Consumer(
          builder: (context, ref, child) {
            final availableTools = ref.watch(availableChatToolsProvider);
            final selectedTool = ref.watch(selectedChatToolProvider);
            
            return ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: availableTools.length,
              separatorBuilder: (context, index) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                final tool = availableTools[index];
                final isSelected = selectedTool.id == tool.id;
                
                return _buildToolChip(tool, isSelected, ref);
              },
            );
          },
        ),
    );
  }

  Widget _buildToolChip(ChatTool tool, bool isSelected, WidgetRef ref) {
    final isLocked = tool.isLocked;
    
    return GestureDetector(
      onTap: () {
        if (!isLocked) {
          ref.read(selectedChatToolProvider.notifier).state = tool;
        } else {
          _showLockedToolDialog(tool);
        }
      },
      child: AnimatedContainer(
        duration: AppConstants.defaultAnimationDuration,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected ? AppTheme.primaryGradient : null,
          color: isSelected ? null : Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? Colors.transparent 
                : Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              blurRadius: 8,
              spreadRadius: 1,
            ),
          ] : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                Icon(
                  tool.icon,
                  size: 20,
                  color: isLocked 
                      ? Colors.grey 
                      : (isSelected ? Colors.white : AppTheme.textPrimaryColor),
                ),
                if (isLocked)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.lock,
                        size: 8,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 8),
            Text(
              tool.name,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isLocked 
                    ? Colors.grey 
                    : (isSelected ? Colors.white : AppTheme.textPrimaryColor),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            if (tool.isPremium && !isLocked)
              Container(
                margin: const EdgeInsets.only(left: 4),
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'PRO',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showLockedToolDialog(ChatTool tool) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(tool.icon, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text(tool.name),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(tool.description),
            const SizedBox(height: 16),
            if (tool.isPremium)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.star, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Premium Feature',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (tool.isPremium)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to shop
                ref.read(navigationIndexProvider.notifier).state = 1;
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: const Text('Upgrade', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
    );
  }

  Widget _buildScrollToBottomFAB() {
    return Positioned(
      bottom: 200,
      right: 16,
      child: ScaleTransition(
        scale: _fabAnimation,
        child: FloatingActionButton.small(
          onPressed: () => _scrollToBottom(),
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(
            Icons.keyboard_arrow_down_rounded,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildInputArea() {
    return Consumer(
      builder: (context, ref, child) {
        final currentTranscription = ref.watch(currentTranscriptionProvider);
        final currentEmotion = ref.watch(currentEmotionProvider);
        final currentLLMResponse = ref.watch(currentLLMResponseProvider);

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // AI Response display (above user transcription)
            if (currentLLMResponse != null && currentLLMResponse.isNotEmpty)
              _buildLLMResponseDisplay(currentLLMResponse),

            // User transcription display (below AI response)
            if (currentTranscription != null && currentTranscription.isNotEmpty)
              _buildTranscriptionDisplay(currentTranscription),

            // Emotion feedback display
            if (currentEmotion != null)
              _buildEmotionDisplay(currentEmotion),

            // Tools section right above the input
            _buildToolsSection(),
            // Input area
            GlassmorphicContainer(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _messageController,
                      focusNode: _messageFocusNode,
                      decoration: InputDecoration(
                        hintText: 'Type a message...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.white.withValues(alpha: 0.1),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                      maxLines: null,
                      textCapitalization: TextCapitalization.sentences,
                      onSubmitted: (_) => _sendTextMessage(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  RealtimeVoiceButton(
                    onTranscription: (transcription) {
                      // Send the transcription as a text message
                      if (transcription.trim().isNotEmpty) {
                        ref.read(chatProvider.notifier).sendMessage(transcription);
                      }
                    },
                    showVadEvents: false, // Set to true for debugging
                  ),
                  const SizedBox(width: 8),
                  FloatingActionButton.small(
                    onPressed: _sendTextMessage,
                    backgroundColor: AppTheme.primaryColor,
                    child: const Icon(
                      Icons.send_rounded,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLLMResponseDisplay(String response) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.accentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.accentColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.psychology_rounded,
            color: AppTheme.accentColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              response,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.accentColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranscriptionDisplay(String transcription) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.mic,
            color: AppTheme.primaryColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              transcription,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.primaryColor,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmotionDisplay(Map<String, dynamic> emotion) {
    final primaryEmotion = emotion['primary_emotion'] as String? ?? 'neutral';
    final intensity = (emotion['intensity'] as num?)?.toDouble() ?? 0.0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getEmotionIcon(primaryEmotion),
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            primaryEmotion.toUpperCase(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.orange,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: intensity,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getEmotionIcon(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'happy':
        return Icons.sentiment_very_satisfied;
      case 'sad':
        return Icons.sentiment_very_dissatisfied;
      case 'angry':
        return Icons.sentiment_dissatisfied;
      case 'excited':
        return Icons.celebration;
      case 'calm':
        return Icons.self_improvement;
      case 'surprised':
        return Icons.sentiment_neutral;
      default:
        return Icons.sentiment_neutral;
    }
  }

  void _sendTextMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();
    
    // TODO: Include selected tool context in message
    ref.read(chatProvider.notifier).sendMessage(text);
    
    // Scroll to bottom after sending
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }



  Widget _buildTaskOverlay() {
    return TaskOverlay(
      onClose: () {
        setState(() {
          _showTaskOverlay = false;
        });
      },
    );
  }

  void _addDemoTasks() {
    // Add some demo tasks for testing
    final taskNotifier = ref.read(taskProvider.notifier);
    
    taskNotifier.addTask(TaskModel(
      id: 'task_1',
      title: 'Code Generation',
      description: 'Generating Flutter widgets for user interface',
      status: TaskStatus.running,
      progress: 0.7,
      createdAt: DateTime.now().subtract(const Duration(minutes: 5)),
      updatedAt: DateTime.now(),
      phases: [
        const TaskPhaseDetail(
          phase: TaskPhase.initialization,
          title: 'Initialization',
          description: 'Setting up project structure',
          isCompleted: true,
          subTasks: ['Created project files', 'Configured dependencies'],
        ),
        const TaskPhaseDetail(
          phase: TaskPhase.analysis,
          title: 'Analysis',
          description: 'Analyzing requirements',
          isCompleted: true,
          subTasks: ['Parsed user requirements', 'Identified components'],
        ),
        const TaskPhaseDetail(
          phase: TaskPhase.execution,
          title: 'Code Generation',
          description: 'Generating Flutter code',
          isCurrent: true,
          subTasks: ['Creating widgets', 'Adding styling', 'Implementing logic'],
        ),
        const TaskPhaseDetail(
          phase: TaskPhase.testing,
          title: 'Testing',
          description: 'Running tests and validation',
          subTasks: ['Unit tests', 'Widget tests', 'Integration tests'],
        ),
      ],
    ));

    taskNotifier.addTask(TaskModel(
      id: 'task_2',
      title: 'API Integration',
      description: 'Setting up REST API connections',
      status: TaskStatus.needsIntervention,
      progress: 0.4,
      createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
      updatedAt: DateTime.now(),
      interventionMessage: 'API key configuration required',
      phases: [
        const TaskPhaseDetail(
          phase: TaskPhase.initialization,
          title: 'Setup',
          description: 'Initial API setup',
          isCompleted: true,
        ),
        const TaskPhaseDetail(
          phase: TaskPhase.planning,
          title: 'Planning',
          description: 'API endpoint mapping',
          isCurrent: true,
        ),
      ],
    ));
  }
}
